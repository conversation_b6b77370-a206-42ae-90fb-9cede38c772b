from selenium import webdriver as wd
from selenium.webdriver.common.by import By as B
from selenium.webdriver.support.ui import Select as S
from selenium.webdriver.chrome.options import Options as O
from datetime import datetime as D, timed<PERSON>ta as T
import time as t

while True:
    print(f"🔁 Starting run at: {D.now().strftime('%Y-%m-%d %H:%M:%S')}")

    z = O()
    [z.add_argument("--start-maximized"), z.add_experimental_option("excludeSwitches", ["enable-automation"]), z.add_experimental_option("useAutomationExtension", False)]
    d = wd.Chrome(options=z)

    W = lambda x: (lambda: next(filter(lambda _: _.is_displayed() and _.is_enabled(), iter(lambda: d.find_element(B.XPATH, x), None))))()
    
    def L():
        while True:
            try:
                l = d.find_element(B.CLASS_NAME, 'raul-page-loader-wrapper')
                if l.is_displayed(): t.sleep(1)
                else: return
            except: return

    def A(): 
        while True:
            try:
                if (x := d.find_element(B.ID, "databaseType")).is_displayed() and x.is_enabled(): return
            except: t.sleep(1)

    d.get("https://ao.realpage.com/ysconfig/app/login")
    W('//*[@id="usernameField"]').send_keys("bmonish")
    W('//*[@id="passwordField"]').send_keys("Realpage#321")
    W('//*[@id="btnLogin"]/span').click()

    s1, s3 = W('//*[@id="double"]/li[1]/a'), W('//*[@id="double"]/li[3]/a')
    [d.execute_script(f"window.open('{s1.get_attribute('href')}', '_blank')"),
     d.execute_script(f"window.open('{s3.get_attribute('href')}', '_blank')")]

    d.switch_to.window(d.window_handles[2]); L()
    d.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")
    print("✅ ysadmin AdHoc Query tab opened")
    d.switch_to.window(d.window_handles[3]); A()

    d.execute_script("window.open('http://rcpypoapamq001.realpage.com:8161/admin/queues.jsp', '_blank');")
    d.switch_to.window(d.window_handles[4])
    print("🕒 Waiting 20 seconds for manual login to Queue page...")
    t.sleep(20)
    
    try:
        p = W('//*[@id="queues"]/tbody/tr[11]/td[2]').text.strip()
        print(f"✅ Number Of Pending Messages - {p}")
    except Exception as e:
        p = "Error"
        print(f"❌ Could not fetch pending messages: {str(e)}")

    d.switch_to.window(d.window_handles[3]); A()
    dt = (D.now() - T(days=2)).strftime("%Y-%m-%d")
    
    N = [
        "udrt - postgres", "berkshire - postgres", "greystar - postgres",
        "greystar2 - postgres", "greystar3 - postgres", "greystar4 - postgres",
        "greystar5 - postgres", "starlight - postgres", "vertica - postgres",
        "quadreal - postgres", "prime - postgres", "lincoln - postgres",
        "camden - postgres", "blackstone - postgres", "pinnacle - postgres"
    ]

    q = f"""select distinct u.propcode,u.propname,u.postdate 
from uidashboard u, propertyparameter p 
where u.propcode = p.propcode 
and p.siteactive = true 
and p.propertytype <> 9 
and u.postdate = '{dt}'::date"""

    R = {}

    for n in N:
        try:
            S(W('//*[@id="databaseType"]')).select_by_visible_text("CLIENT")
            S(W('//*[@id="databaseName"]')).select_by_visible_text(n)
            i = W('//*[@id="query"]')
            i.clear(); i.send_keys(q)
            W('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button').click()
            t.sleep(5)
            try:
                h = d.find_element(B.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6').text
                R[n] = int(''.join(filter(str.isdigit, h)))
                print(f"✅ [{n}] → {h}")
            except:
                R[n] = 0
                print(f"⚠ [{n}] → No result summary found.")
        except Exception as e:
            R[n] = 0
            print(f"❌ [{n}] → Failed. Error: {str(e)}")

    print("\n📊 Final Summary:")
    x = ' '.join([f"{k.split()[0].replace('-', '')}({v})" for k, v in R.items()])
    x += f"  Number Of Pending Messages - {p}"
    print(x)

    d.quit()
    print("✅ Cycle complete. Waiting 1 hour...\n")
    t.sleep(3600)