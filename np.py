from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from urllib.parse import quote
import time
from datetime import datetime, timedelta
 
# Setup browser
options = Options()
options.add_argument("--start-maximized")
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option("useAutomationExtension", False)
 
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, 30)
 
# Helpers
def wait_for_xpath(xpath):
    return wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
 
def wait_for_option(select_id, option_text, timeout=30):
    WebDriverWait(driver, timeout).until(
        lambda d: any(
            option_text in option.text
            for option in d.find_element(By.ID, select_id).find_elements(By.TAG_NAME, "option")
        )
    )
 
def wait_until_loader_disappears():
    while True:
        try:
            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
            if loader.is_displayed():
                time.sleep(1)
            else:
                return
        except:
            return
 
# Step 1: Login to RealPage
driver.get("https://ao.realpage.com/ysconfig/app/login")
wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 2: Open YieldStar and AIRM
yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")
 
# Step 3: Switch to AIRM and wait
driver.switch_to.window(driver.window_handles[2])
wait_until_loader_disappears()
 
# Step 4: Open ysadmin main page
driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin', '_blank');")
driver.switch_to.window(driver.window_handles[3])
print("✅ Opened ysadmin main page")
wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
time.sleep(3)
 
# Step 5: Click AdHoc Query from sidebar
adhoc_sidebar_xpath = '//*[@id="raul-left-navigation-items"]/li[12]/a'
adhoc_button = wait.until(EC.element_to_be_clickable((By.XPATH, adhoc_sidebar_xpath)))
driver.execute_script("arguments[0].click();", adhoc_button)
print("✅ Navigated to AdHoc Query from sidebar")
 
# Step 6: Wait for dropdowns to appear
wait.until(EC.presence_of_element_located((By.ID, "databaseType")))
 
# Step 7: Open Queues tab
username = "admin"
password = "admin"
auth_url = f"http://{quote(username)}:{quote(password)}@rcpypoapamq001.realpage.com:8161/admin/queues.jsp"
driver.execute_script(f"window.open('{auth_url}', '_blank');")
driver.switch_to.window(driver.window_handles[4])
print("🕒 Waiting 10 seconds for Queues page to load...")
time.sleep(10)
 
# Step 8: Fetch pending messages
try:
    pending_elem = wait_for_xpath('//*[@id="queues"]/tbody/tr[11]/td[2]')
    pending_messages = pending_elem.text.strip()
    print(f"✅ Number Of Pending Messages - {pending_messages}")
except Exception as e:
    pending_messages = "Error"
    print(f"❌ Could not fetch pending messages: {str(e)}")
 
# Step 9: Back to AdHoc Query
driver.switch_to.window(driver.window_handles[3])
wait.until(EC.presence_of_element_located((By.ID, "databaseType")))
 
# Step 10: Run DB Queries
target_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")
 
database_names = [
    "udrt - postgres", "berkshire - postgres", "greystar - postgres",
    "greystar2 - postgres", "greystar3 - postgres", "greystar4 - postgres",
    "greystar5 - postgres", "starlight - postgres", "vertica - postgres",
    "quadreal - postgres", "prime - postgres", "lincoln - postgres",
    "camden - postgres", "blackstone - postgres", "pinnacle - postgres"
]
 
query = f"""select distinct u.propcode,u.propname,u.postdate
from uidashboard u, propertyparameter p
where u.propcode = p.propcode
and p.siteactive = true
and p.propertytype <> 9
and u.postdate = '{target_date}'::date"""
 
results_summary = {}
 
for db_name in database_names:
    try:
        wait_for_option("databaseType", "CLIENT")
        Select(driver.find_element(By.ID, "databaseType")).select_by_visible_text("CLIENT")
 
        wait_for_option("databaseName", db_name)
        Select(driver.find_element(By.ID, "databaseName")).select_by_visible_text(db_name)
 
        query_input = wait.until(EC.element_to_be_clickable((By.ID, "query")))
        query_input.clear()
        query_input.send_keys(query)
 
        submit_btn = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')))
        submit_btn.click()
        time.sleep(5)
 
        try:
            result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
            count = int(''.join(filter(str.isdigit, result_summary.text)))
            results_summary[db_name] = count
            print(f"✅ [{db_name}] → {result_summary.text}")
        except:
            results_summary[db_name] = 0
            print(f"⚠ [{db_name}] → No result summary found.")
 
    except Exception as e:
        results_summary[db_name] = 0
        print(f"❌ [{db_name}] → Failed. Error: {str(e)}")
 
# Final Summary
print("\n📊 Final Summary:")
formatted_summary = ' '.join([
    f"{name.split()[0].replace('-', '')}({count})"
    for name, count in results_summary.items()
])
formatted_summary += f"  Number Of Pending Messages - {pending_messages}"
print(formatted_summary)
 
# Step 11: YieldStar EU login
driver.execute_script("window.open('https://yieldstareu.realpage.com/ysconfig/app/login', '_blank');")
driver.switch_to.window(driver.window_handles[-1])
print("🌍 Opened YieldStar EU Login")
 
wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 12: Navigate to RM tab
redirect_elem = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, '/ysconfig/app/redirect?url=https')]")))
driver.execute_script("arguments[0].click();", redirect_elem)
 
rm_nav_tab_xpath = '//*[@id="ysBody"]/revenue-management/omnibar-shell/div/div[2]/div[1]/omnibar-navigation/div/div/omnibar-navigation-item[3]/div/a'
rm_nav_tab = wait_for_xpath(rm_nav_tab_xpath)
driver.execute_script("arguments[0].click();", rm_nav_tab)
print("✅ YieldStar EU Navigation completed.")
 
# Step 13: Check Post Dates
print("\n🔍 Verifying property post dates...\n")
time.sleep(5)
page_html = driver.page_source.upper()
post_date = (datetime.now() - timedelta(days=1)).strftime("%d-%b-%Y").upper()
 
property_names = [
    "Charter Place", "Croydon No. 26", "Eaton Manor Hove", "Forbes Place",
    "Greengate", "Kampus", "Market Quarter", "Oxbow", "Stafford Yard",
    "The Almere", "The Copper House", "The Well House - Sutton",
    "The Wullcomb", "Vantage", "Walton Court"
]
 
for prop in property_names:
    prop_upper = prop.upper()
    if prop_upper in page_html:
        if post_date in page_html:
            print(f"✅ '{prop}' found with post date {post_date}")
        else:
            print(f"⚠ '{prop}' found, but post date {post_date} not found")
    else:
        print(f"❌ '{prop}' not found on the page")
 
# Done
print("\n🕓 Script completed. Keeping browser open...")
while True:
    time.sleep(60)
