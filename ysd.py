# from selenium import webdriver
# from selenium.webdriver.common.by import By
# from selenium.webdriver.support.ui import Select
# from selenium.webdriver.chrome.options import Options
# import time
# from datetime import datetime, timedelta

# # Infinite hourly loop
# while True:
#     print(f"🔁 Starting run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

#     # Setup Chrome options
#     options = Options()
#     options.add_argument("--start-maximized")
#     options.add_experimental_option("excludeSwitches", ["enable-automation"])
#     options.add_experimental_option("useAutomationExtension", False)
#     driver = webdriver.Chrome(options=options)

#     def wait_for_xpath(xpath):
#         while True:
#             try:
#                 elem = driver.find_element(By.XPATH, xpath)
#                 if elem.is_displayed() and elem.is_enabled():
#                     return elem
#             except:
#                 pass
#             time.sleep(1)

#     def wait_until_loader_disappears():
#         while True:
#             try:
#                 loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
#                 if loader.is_displayed():
#                     time.sleep(1)
#                 else:
#                     return
#             except:
#                 return

#     # Step 1: Login
#     driver.get("https://ao.realpage.com/ysconfig/app/login")
#     wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
#     wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
#     wait_for_xpath('//*[@id="btnLogin"]/span').click()

#     # Step 2: Open YieldStar and AIRM in tabs
#     yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
#     airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
#     driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
#     driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

#     # Step 3: Switch to AIRM tab
#     driver.switch_to.window(driver.window_handles[2])
#     wait_until_loader_disappears()

#     # Step 4: Open ysadmin AdHoc Query in 4th tab
#     driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")
#     print("✅ ysadmin AdHoc Query tab opened")
#     driver.switch_to.window(driver.window_handles[3])

#     # Step 5: Wait until AdHoc loads fully
#     wait_for_xpath('//*[@id="databaseType"]')

#     # Step 6: Run queries for databases
#     target_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")
#     query = f"""select distinct u.propcode,u.propname,u.postdate 
# from uidashboard u, propertyparameter p 
# where u.propcode = p.propcode 
# and p.siteactive = true 
# and p.propertytype <> 9 
# and u.postdate = '{target_date}'::date"""

#     databases = {
#         "udrt": "udrt - postgres",
#         "berkshire": "berkshire - postgres",
#         "greystar": "greystar - postgres",
#         "greystar2": "greystar2 - postgres",
#         "greystar3": "greystar3 - postgres",
#         "greystar4": "greystar4 - postgres",
#         "greystar5": "greystar5 - postgres",
#         "starlight": "starlight - postgres",
#         "vertica": "vertica - postgres",
#         "quadreal": "quadreal - postgres",
#         "prime": "prime - postgres",
#         "lincoln": "lincoln - postgres",
#         "camden": "camden - postgres",
#         "blackstone": "blackstone - postgres",
#         "pinnacle": "pinnacle - postgres"
#     }

#     results_summary = {}

#     for key, db_name in databases.items():
#         try:
#             # Select CLIENT
#             wait_for_xpath('//*[@id="databaseType"]')
#             Select(driver.find_element(By.ID, "databaseType")).select_by_visible_text("CLIENT")

#             # Select DB dynamically
#             Select(driver.find_element(By.ID, "databaseName")).select_by_visible_text(db_name)

#             # Input query
#             query_input = wait_for_xpath('//*[@id="query"]')
#             query_input.clear()
#             query_input.send_keys(query)

#             # Execute query
#             wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button').click()

#             # Wait for results to appear
#             for _ in range(30):
#                 try:
#                     result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
#                     if result_summary.text.strip():
#                         break
#                 except:
#                     time.sleep(1)

#             try:
#                 result_text = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6').text
#                 count = int(''.join(filter(str.isdigit, result_text)))
#                 results_summary[key] = count
#                 print(f"✅ [{db_name}] → {result_text}")
#             except:
#                 results_summary[key] = 0
#                 print(f"⚠ [{db_name}] → No result summary found.")
#         except Exception as e:
#             results_summary[key] = 0
#             print(f"❌ [{db_name}] → Failed. Error: {str(e)}")

#     # Step 7: Open message queue monitor (after all queries)
#     driver.execute_script("window.open('http://rcpypoapamq001.realpage.com:8161/admin/queues.jsp', '_blank');")
#     driver.switch_to.window(driver.window_handles[4])

#     try:
#         pending_elem = wait_for_xpath('//*[@id="queues"]/tbody/tr[11]/td[2]')
#         pending_messages = pending_elem.text.strip()
#         print(f"✅ Number Of Pending Messages - {pending_messages}")
#     except Exception as e:
#         pending_messages = "Error"
#         print(f"❌ Could not fetch pending messages: {str(e)}")

#     # Final Summary
#     print("\n📊 Final Summary:")
#     formatted_summary = ' '.join([
#         f"{key.upper()}({value})"
#         for key, value in results_summary.items()
#     ])
#     formatted_summary += f"  PendingMsgs({pending_messages})"
#     print(formatted_summary)

#     driver.quit()
#     print("✅ Cycle complete. Waiting 1 hour...\n")
#     time.sleep(3600)

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options
from urllib.parse import quote
from datetime import datetime, timedelta
import time

while True:
    print(f"🔁 Starting run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    options = Options()
    options.add_argument("--start-maximized")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option("useAutomationExtension", False)
    driver = webdriver.Chrome(options=options)

    def wait_for_xpath(xpath):
        while True:
            try:
                elem = driver.find_element(By.XPATH, xpath)
                if elem.is_displayed() and elem.is_enabled():
                    return elem
            except:
                pass
            time.sleep(1)

    def wait_until_loader_disappears():
        while True:
            try:
                loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
                if loader.is_displayed():
                    time.sleep(1)
                else:
                    return
            except:
                return

    # Step 1: Login
    driver.get("https://ao.realpage.com/ysconfig/app/login")
    wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
    wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
    wait_for_xpath('//*[@id="btnLogin"]/span').click()

    # Step 2: Open YieldStar and AIRM in new tabs
    yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
    airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
    driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
    driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

    # Step 3: Switch to AIRM tab
    driver.switch_to.window(driver.window_handles[2])
    wait_until_loader_disappears()

    # Step 4: Open ysadmin AdHoc Query
    driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")
    print("✅ ysadmin AdHoc Query tab opened")
    driver.switch_to.window(driver.window_handles[3])

    # Wait for AdHoc Query to load
    wait_for_xpath('//*[@id="databaseType"]')

    # Prepare the databases and query
    databases = {
        "udrt - postgres": "UDRT",
        "berkshire - postgres": "Berkshire",
        "greystar - postgres": "Greystar",
        
    }

    target_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")
    query = f"""select distinct u.propcode,u.propname,u.postdate 
from uidashboard u, propertyparameter p 
where u.propcode = p.propcode 
and p.siteactive = true 
and p.propertytype <> 9 
and u.postdate = '{target_date}'::date"""

    results_summary = {}

    for value, label in databases.items():
        try:
            Select(wait_for_xpath('//*[@id="databaseType"]')).select_by_visible_text("CLIENT")
            Select(wait_for_xpath('//*[@id="databaseName"]')).select_by_value(value)
            query_input = wait_for_xpath('//*[@id="query"]')
            query_input.clear()
            query_input.send_keys(query)
            wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button').click()
            time.sleep(5)
            try:
                result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
                count = int(''.join(filter(str.isdigit, result_summary.text)))
                results_summary[label] = count
                print(f"✅ [{label}] → {result_summary.text}")
            except:
                results_summary[label] = 0
                print(f"⚠ [{label}] → No result summary found.")
        except Exception as e:
            results_summary[label] = 0
            print(f"❌ [{label}] → Error: {str(e)}")

    # Step 5: Open Queues page with login
    username = "admin"
    password = "admin"
    auth_url = f"http://{quote(username)}:{quote(password)}@rcpypoapamq001.realpage.com:8161/admin/queues.jsp"
    driver.execute_script(f"window.open('{auth_url}', '_blank');")
    driver.switch_to.window(driver.window_handles[4])

    # Step 6: Read pending messages
    time.sleep(5)
    try:
        pending_elem = wait_for_xpath('//*[@id="queues"]/tbody/tr[11]/td[2]')
        pending_messages = pending_elem.text.strip()
        print(f"✅ Number Of Pending Messages - {pending_messages}")
    except Exception as e:
        pending_messages = "Error"
        print(f"❌ Could not fetch pending messages: {str(e)}")

    # Summary Output
    print("\n📊 Final Summary:")
    summary = ' '.join([f"{k}({v})" for k, v in results_summary.items()])
    summary += f"  PendingMessages({pending_messages})"
    print(summary)

    driver.quit()
    print("✅ Cycle complete. Waiting 1 hour...\n")
    time.sleep(3600)