from selenium import webdriver as ω
from selenium.webdriver.common.by import By as β
from selenium.webdriver.support.ui import Select as Σ, WebDriverWait as Π
from selenium.webdriver.support import expected_conditions as ε
from selenium.webdriver.chrome.options import Options as Ω
from selenium.common.exceptions import WebDriverException
from urllib.parse import quote as ϕ
from datetime import datetime as δ, timed<PERSON>ta as τ
import time as θ

Ψ = Ω()
Ψ.add_argument("--start-maximized")
Ψ.add_experimental_option("excludeSwitches", ["enable-automation"])
Ψ.add_experimental_option("useAutomationExtension", False)

χ = ω.Chrome(options=Ψ)
π = Π(χ, 30)

ζ = lambda x: π.until(ε.presence_of_element_located((β.XPATH, x)))
η = lambda sid, txt, to=30: Π(χ, to).until(
    lambda κ: any(txt in ξ.text for ξ in κ.find_element(β.ID, sid).find_elements(β.TAG_NAME, "option"))
)

def λ():
    while True:
        try:
            Λ = χ.find_element(β.CLASS_NAME, 'raul-page-loader-wrapper')
            if Λ.is_displayed(): θ.sleep(1)
            else: return
        except: return

def close_all_tabs_except_first():
    main_tab = χ.window_handles[0]
    handles = χ.window_handles[1:]
    for handle in handles:
        try:
            χ.switch_to.window(handle)
            χ.close()
        except WebDriverException:
            continue
    χ.switch_to.window(main_tab)

# Initial Login
χ.get("https://ao.realpage.com/ysconfig/app/login")
ζ('//*[@id="usernameField"]').send_keys("username")
ζ('//*[@id="passwordField"]').send_keys("password")
ζ('//*[@id="btnLogin"]/span').click()

while True:
    try:
        # Open AIRM & YieldStar tabs
        Ξ, Γ = ζ('//*[@id="double"]/li[1]/a'), ζ('//*[@id="double"]/li[3]/a')
        χ.execute_script(f"window.open('{Ξ.get_attribute('href')}', '_blank');")
        χ.execute_script(f"window.open('{Γ.get_attribute('href')}', '_blank');")

        χ.switch_to.window(χ.window_handles[2])
        λ()

        # Open ysadmin tab
        χ.execute_script("window.open('https://ysadmin.realpage.com/ysadmin', '_blank');")
        χ.switch_to.window(χ.window_handles[3])
        π.until(ε.presence_of_element_located((β.TAG_NAME, "body")))
        θ.sleep(3)

        # Open AdHoc Query
        ζa = '//*[@id="raul-left-navigation-items"]/li[12]/a'
        ρ = π.until(ε.element_to_be_clickable((β.XPATH, ζa)))
        χ.execute_script("arguments[0].click();", ρ)
        π.until(ε.presence_of_element_located((β.ID, "databaseType")))

        # Open Queue Monitor tab
        μ = "http://rcpypoapamq001.realpage.com:8161/admin/queues.jsp"
        χ.execute_script(f"window.open('{μ}', '_blank');")
        χ.switch_to.window(χ.window_handles[4])
        θ.sleep(20)

        try:
            α = ζ('//*[@id="queues"]/tbody/tr[11]/td[2]')
            σ = α.text.strip()
        except Exception:
            σ = "Error"

        # Go back to AdHoc Query tab
        χ.switch_to.window(χ.window_handles[3])
        π.until(ε.presence_of_element_located((β.ID, "databaseType")))

        ϑ = (δ.now() - τ(days=2)).strftime("%Y-%m-%d")

        Δ = [
            "udrt - postgres", "berkshire - postgres", "greystar - postgres",
            "greystar2 - postgres", "greystar3 - postgres", "greystar4 - postgres",
            "greystar5 - postgres", "starlight - postgres", "vertica - postgres",
            "quadreal - postgres", "prime - postgres", "lincoln - postgres",
            "camden - postgres", "blackstone - postgres", "pinnacle - postgres"
        ]

        ψ = f"""select distinct u.propcode,u.propname,u.postdate 
from uidashboard u, propertyparameter p 
where u.propcode = p.propcode 
and p.siteactive = true 
and p.propertytype <> 9 
and u.postdate = '{ϑ}'::date"""

        Ωπ = {}

        for δβ in Δ:
            try:
                η("databaseType", "CLIENT")
                Σ(χ.find_element(β.ID, "databaseType")).select_by_visible_text("CLIENT")

                η("databaseName", δβ)
                Σ(χ.find_element(β.ID, "databaseName")).select_by_visible_text(δβ)

                ℓ = π.until(ε.element_to_be_clickable((β.ID, "query")))
                ℓ.clear()
                ℓ.send_keys(ψ)

                ς = π.until(ε.element_to_be_clickable((β.XPATH, '//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')))
                ς.click()
                θ.sleep(5)

                try:
                    ϵ = χ.find_element(β.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
                    ωΣ = int(''.join(filter(str.isdigit, ϵ.text)))
                    Ωπ[δβ] = ωΣ
                except:
                    Ωπ[δβ] = 0

            except Exception:
                Ωπ[δβ] = 0

        print("\n📊 Final Summary:")
        Ψς = ' '.join([
            f"{ν.split()[0].replace('-', '')}({ω})"
            for ν, ω in Ωπ.items()
        ])
        Ψς += f"  Number Of Pending Messages - {σ}"
        print(Ψς)

        # Close all tabs except login
        close_all_tabs_except_first()

        print(f"\n⏳ Sleeping until next run - {δ.now().strftime('%Y-%m-%d %H:%M:%S')}")
        θ.sleep(3600)  # Wait 1 hour

        # Try to click login again if session expired
        try:
            ζ('//*[@id="btnLogin"]/span').click()
        except:
            pass

    except Exception as e:
        print(f"⚠️ Error occurred: {e}")
        break