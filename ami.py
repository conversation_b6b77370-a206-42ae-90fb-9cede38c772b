from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")

# Launch browser
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, 20)

# Step 1: Open login page
driver.get("https://pgtask.realpage.com/login")

# Step 2: Wait for username field and enter value
username_field = wait.until(EC.presence_of_element_located((By.XPATH, '//input[@type="text" or @type="email"]')))
username_field.clear()
username_field.send_keys("bmonish")

# Step 3: Wait for password field and enter value
password_field = wait.until(EC.presence_of_element_located((By.XPATH, '//input[@type="password"]')))
password_field.clear()
password_field.send_keys("Realpage#321")

# Step 4: Click the login button
login_btn_xpath = '//*[@id="app"]/div/main/div[2]/div/div/div/div/form/button'
wait.until(EC.element_to_be_clickable((By.XPATH, login_btn_xpath))).click()

# Step 5: Wait for dashboard to load
print("⏳ Waiting for Amenity Nightly to appear...")
amenity_element = None
for _ in range(30):
    try:
        elements = driver.find_elements(By.XPATH, '//*[contains(text(), "Amenity Nightly")]')
        if elements:
            amenity_element = elements[0]
            break
    except:
        pass
    time.sleep(1)

# Step 6: If found, extract status
if amenity_element:
    print(f"🎯 Found Amenity Nightly: '{amenity_element.text.strip()}'")
    driver.execute_script("arguments[0].style.border='3px solid red'", amenity_element)

    try:
        parent = amenity_element.find_element(By.XPATH, './ancestor::div[contains(@class, "card") or contains(@class, "job") or contains(@class, "task")][1]')
        print("🔎 Debug - HTML of parent container:")
        print(parent.get_attribute("outerHTML"))
        driver.save_screenshot("amenity_status_debug.png")
        print("📸 Screenshot saved: amenity_status_debug.png")

        status_found = False
        possible_status_elements = parent.find_elements(By.XPATH, './/*[text()]')

        for elem in possible_status_elements:
            status_text = elem.text.strip()
            if any(word in status_text.lower() for word in ["finished", "running", "success", "failed", "error", "complete", "pending"]):
                print(f"✅ Amenity Nightly Status: {status_text}")
                status_found = True
                break

        if not status_found:
            print("⚠ Found job card but no clear status text identified.")
    except Exception as e:
        print(f"⚠ Error while extracting status: {e}")
else:
    print("❌ Amenity Nightly job not found. Dumping first 50 visible texts...")
    all_texts = driver.find_elements(By.XPATH, "//*[text()]")
    for i, elem in enumerate(all_texts[:50], start=1):
        try:
            text = elem.text.strip()
            if text and len(text) > 3:
                print(f"{i}. '{text}'")
        except:
            continue

# Uncomment this to close the browser after run
# driver.quit()